# Cosmos DB Integration Guide

## Overview

This document provides a comprehensive overview of how Azure Cosmos DB is integrated into the Kraken application, specifically focusing on the updated `GetSubmissionsAsync` method in the `AnteaService`.

## Cosmos DB Architecture

### Core Components

1. **ValidatingCosmosClient**: Enhanced CosmosClient that validates container existence and retrieves partition key paths
2. **CosmosClientAdapter**: Adapter pattern implementation for dependency injection
3. **BaseCosmosRepository**: Generic base class for Cosmos DB operations
4. **Container Factory**: Factory pattern for creating and managing Cosmos DB containers

### Configuration

#### Connection Settings
```json
{
  "Connections": {
    "Database": "OrderTrackingDB",
    "Endpoint": "https://your-cosmos-account.documents.azure.com:443/",
    "AuthKey": "your-cosmos-db-primary-key",
    "ConnectionString": "AccountEndpoint=https://...;AccountKey=...;",
    "DatabaseName": "OrderTrackingDB"
  }
}
```

#### Container Definitions
The following containers are defined in the infrastructure:

- `user-profiles` (partition key: `/id`)
- `roles` (partition key: `/id`)
- `equipment-requests` (partition key: `/id`)
- `flange-calculations` (partition key: `/id`)
- `notifications` (partition key: `/id`)
- `release-notes` (partition key: `/id`)
- `auth-history` (partition key: `/id`)
- `cms-base-inspections` (partition key: `/id`)
- `cms-client-inspections` (partition key: `/id`)
- `cms-district-inspections` (partition key: `/id`)
- `OIS-Submissions` (partition key: `/id`) - **Newly Added**

## GetSubmissions Method Integration

### Previous Implementation Issues

The original `GetSubmissionsAsync` method had several issues:

1. **Inconsistent Pattern**: Created new CosmosClient instances instead of using dependency injection
2. **Hardcoded Values**: Database name and connection string were hardcoded
3. **Poor Error Handling**: Basic try-catch without proper logging
4. **Performance Issues**: New client creation on each call
5. **Missing Infrastructure**: OIS-Submissions container wasn't defined

### Updated Implementation

#### Key Improvements

1. **Dependency Injection**: Uses injected `ICosmosClientAdapter` and `ILogger`
2. **Configuration-Driven**: Uses values from `appsettings.json`
3. **Proper Error Handling**: Comprehensive error handling with structured logging
4. **Performance Optimization**: Reuses container instances and optimized queries
5. **Code Organization**: Separated concerns with helper methods

#### Method Signature
```csharp
public async Task<List<ResponseSubmissions>> GetSubmissionsAsync(string assetIdFilter)
```

#### Key Features

##### 1. Optimized Querying
- Uses parameterized queries when filtering by asset ID
- Implements efficient filtering at the database level
- Proper use of query iterators with resource management

##### 2. Error Handling
- Specific handling for `CosmosException`
- Detailed logging with context information
- Graceful degradation on individual document processing errors

##### 3. Document Processing
- Separated document processing logic into `ProcessSubmissionDocument`
- Robust null checking and type conversion
- Proper handling of nested JSON structures

##### 4. Service Type Determination
- Intelligent service type assignment based on submission type
- Document count calculation for document uploads
- Fallback handling for unknown submission types

#### Code Structure

```csharp
public async Task<List<ResponseSubmissions>> GetSubmissionsAsync(string assetIdFilter)
{
    // Main method with error handling and logging
}

private ResponseSubmissions ProcessSubmissionDocument(dynamic docData, string assetIdFilter)
{
    // Document processing and validation
}

private void SetSubmissionServiceType(ResponseSubmissions submission, dynamic docData, string anomalyId)
{
    // Service type determination logic
}
```

## Dependency Injection Setup

### Service Registration

The `AnteaService` is registered with proper dependency injection in `Startup.cs`:

```csharp
.AddSingleton<IAnteaService>(serviceProvider =>
{
    var dataOptions = serviceProvider.GetRequiredService<IOptions<AnteaData>>();
    var connectionOptions = serviceProvider.GetRequiredService<IOptions<Connections>>();
    var cosmosClientAdapter = serviceProvider.GetRequiredService<ICosmosClientAdapter>();
    var logger = serviceProvider.GetRequiredService<ILogger<AnteaService>>();
    
    return new AnteaService(dataOptions, connectionOptions, serviceProvider, cosmosClientAdapter, logger);
})
```

### Constructor Updates

The `AnteaService` constructor now accepts optional parameters for better testability:

```csharp
public AnteaService(IOptions<AnteaData> dataOptions, IOptions<Connections> options, 
    IServiceProvider serviceProvider, ICosmosClientAdapter cosmosClientAdapter = null, 
    ILogger<AnteaService> logger = null)
```

## Infrastructure Changes

### Bicep Template Updates

Added the OIS-Submissions container to the infrastructure definition:

```bicep
var containers = [
  // ... existing containers
  { name: 'OIS-Submissions', partitionKey: '/id' }
]
```

### Validation Script Updates

Updated the infrastructure validation script to check for the new container:

```bash
CONTAINERS=("user-profiles" "roles" "equipment-requests" "notifications" "OIS-Submissions")
```

## Performance Considerations

### Query Optimization

1. **Parameterized Queries**: Uses proper parameterization to prevent injection and improve performance
2. **Filtered Queries**: Applies asset filtering at the database level when possible
3. **Resource Management**: Proper disposal of query iterators using `using` statements

### Caching Strategy

- Container instances are cached in the service
- Connection reuse through dependency injection
- Singleton service registration for optimal performance

### Error Resilience

- Continues processing other documents if one fails
- Logs warnings for individual document failures
- Returns partial results rather than failing completely

## Monitoring and Logging

### Logging Levels

- **Information**: Successful operations with counts and filters
- **Warning**: Individual document processing failures
- **Error**: Container initialization failures and Cosmos DB exceptions

### Log Context

All log entries include relevant context:
- Asset filter values
- Document counts
- Error details and status codes
- Cosmos DB operation specifics

## Best Practices

### 1. Container Management
- Always check for container existence before operations
- Use the factory pattern for container creation
- Cache container references for performance

### 2. Query Design
- Use parameterized queries for security and performance
- Apply filters at the database level when possible
- Implement proper pagination for large result sets

### 3. Error Handling
- Distinguish between different error types (Cosmos, application, etc.)
- Provide meaningful error messages and context
- Implement retry logic for transient failures

### 4. Performance
- Reuse client connections through dependency injection
- Use appropriate partition keys for efficient querying
- Monitor and optimize query performance

## Testing Considerations

### Unit Testing
- Constructor accepts optional parameters for easy mocking
- Separated business logic allows for focused testing
- Proper dependency injection enables isolation testing

### Integration Testing
- Test against Cosmos DB emulator for development
- Validate container existence and configuration
- Test error scenarios and resilience

## Migration Notes

### Changes Made

1. **Service Registration**: Updated to use dependency injection
2. **Configuration**: Added missing Cosmos DB connection settings
3. **Infrastructure**: Added OIS-Submissions container definition
4. **Error Handling**: Implemented comprehensive error handling
5. **Logging**: Added structured logging throughout

### Breaking Changes

- Constructor signature changed (additional optional parameters)
- Service registration pattern updated
- Configuration requirements added

### Backward Compatibility

- Optional parameters maintain backward compatibility
- Graceful fallbacks for missing dependencies
- Configuration validation with meaningful error messages

## Future Enhancements

### Potential Improvements

1. **Repository Pattern**: Consider creating a dedicated submissions repository
2. **Caching**: Implement Redis caching for frequently accessed data
3. **Pagination**: Add pagination support for large result sets
4. **Health Checks**: Implement Cosmos DB health check endpoints
5. **Metrics**: Add custom metrics for monitoring and alerting

### Scalability Considerations

- Consider implementing partition key strategies for larger datasets
- Monitor RU consumption and optimize queries
- Implement connection pooling and retry policies
- Consider read replicas for high-read scenarios

## Troubleshooting

### Common Issues

1. **Container Not Found**: Ensure infrastructure is deployed correctly
2. **Connection Failures**: Validate connection string and network access
3. **Permission Issues**: Verify Cosmos DB access keys and permissions
4. **Performance Issues**: Monitor RU consumption and query patterns

### Debugging Tips

- Enable detailed logging to trace execution flow
- Use Cosmos DB metrics and monitoring
- Test queries in Azure portal or Cosmos DB Explorer
- Validate document structure matches expected format

## Conclusion

The updated Cosmos DB integration follows established patterns used throughout the application, providing:

- **Consistency**: Aligned with other Cosmos DB services in the application
- **Reliability**: Comprehensive error handling and logging
- **Performance**: Optimized queries and resource management
- **Maintainability**: Clean separation of concerns and proper dependency injection
- **Scalability**: Foundation for future enhancements and optimizations

This implementation provides a robust foundation for the submissions functionality while maintaining consistency with the overall application architecture.
